/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.RoleRepository;
import com.tripudiotech.authservice.service.context.UserCreationContext;
import com.tripudiotech.base.client.dto.request.GroupReference;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.OrgRole;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service responsible for managing user permissions (roles and groups).
 * Follows Single Responsibility Principle by focusing only on permission-related operations.
 */
@ApplicationScoped
@Slf4j
public class UserPermissionService {

    @Inject
    RoleRepository roleRepository;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    UserGroupService userGroupService;

    @Inject
    AsyncConfigurationService asyncConfigurationService;

    /**
     * Assigns default roles to a newly created user.
     * 
     * @param context the user creation context containing all necessary information
     * @return Uni that completes when roles are assigned
     */
    public Uni<Void> assignDefaultRoles(@NonNull UserCreationContext context) {
        validateContextForPermissionAssignment(context);

        Set<String> defaultRoles = new HashSet<>(RoleConstant.DEFAULT_ROLES_ON_CREATE);
        log.info("Assigning default roles {} to user {}", defaultRoles, context.getCreatedEntity().getId());

        return assignRolesToUser(
                context.getTenantId(),
                context.getUserInformation(),
                context.getCreatedEntity().getId(),
                context.getAuthServerId(),
                defaultRoles
        );
    }

    /**
     * Assigns user to requested groups if any are specified.
     * 
     * @param context the user creation context containing group assignments
     * @return Uni that completes when groups are assigned
     */
    public Uni<Void> assignUserToGroups(@NonNull UserCreationContext context) {
        validateContextForPermissionAssignment(context);

        if (!context.getRequest().hasGroups()) {
            log.debug("No groups specified for user {}, skipping group assignment", 
                    context.getCreatedEntity().getId());
            return Uni.createFrom().voidItem();
        }

        Set<GroupReference> groupReferences = context.getRequest().getAllGroupReferences();
        log.info("Assigning user {} to {} groups: {}", 
                context.getCreatedEntity().getId(), 
                groupReferences.size(),
                groupReferences.stream().map(GroupReference::getValue).toList());

        List<Uni<Void>> groupAssignments = groupReferences.stream()
                .filter(groupRef -> StringUtils.isNotBlank(groupRef.getValue()))
                .map(groupRef -> assignUserToSingleGroup(context, groupRef))
                .toList();

        return Uni.combine().all().unis(groupAssignments)
                .discardItems()
                .onItem().invoke(() -> log.info("Completed group assignment for user {}", 
                        context.getCreatedEntity().getId()))
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    /**
     * Assigns roles to a user using the auth server user ID directly for optimal performance.
     */
    private Uni<Void> assignRolesToUser(
            @NonNull String tenantId,
            @NonNull com.tripudiotech.securitylib.dto.UserInformation userInformation,
            @NonNull String userId,
            @NonNull String authServerId,
            @NonNull Set<String> requestRoles
    ) {
        if (requestRoles.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        return Uni.createFrom().item(() -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    Map<String, String> validGlideRoleNamesMap = getValidGlideRoleMap(tenantId, securityProviderService);
                    return getValidRolesToAdd(tenantId, validGlideRoleNamesMap, requestRoles);
                })
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(validRolesToAdd -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    securityProviderService.assignDefaultRolesToUser(tenantId, authServerId, validRolesToAdd);

                    Set<String> validRolesToAddLowerCase = validRolesToAdd.stream()
                            .map(String::toLowerCase)
                            .collect(Collectors.toSet());

                    return ReactorUtils.toUni(roleRepository.getAllRoles(tenantId, userInformation))
                            .flatMap(orgRoles -> {
                                List<OrgRole> validOrgRoles = orgRoles.stream()
                                        .filter(orgRole -> validRolesToAddLowerCase.contains(orgRole.getName().toLowerCase()))
                                        .toList();

                                if (validOrgRoles.isEmpty()) {
                                    throw new BadRequestException(tenantId, "No valid roles to assign");
                                }

                                return roleRepository.assignRoleToPerson(tenantId, userInformation, validOrgRoles,
                                        SysRoot.builder().id(userId).build());
                            });
                });
    }

    /**
     * Assigns user to a single group with proper error handling.
     */
    private Uni<Void> assignUserToSingleGroup(UserCreationContext context, GroupReference groupRef) {
        return resolveGroupIdAndAuthId(context.getTenantId(), groupRef)
                .flatMap(groupInfo -> {
                    String groupId = groupInfo.getItem1();
                    String authGroupId = groupInfo.getItem2();
                    log.debug("Resolved group '{}' to ID: {} with authId: {}", 
                            groupRef.getValue(), groupId, authGroupId);
                    return userGroupService.addUserToAuthGroup(
                            context.getTenantId(), 
                            groupId, 
                            authGroupId, 
                            context.getAuthServerId()
                    );
                })
                .onItem().invoke(() -> log.info("Successfully assigned user {} to group '{}' ({})",
                        context.getCreatedEntity().getId(), groupRef.getValue(), groupRef.getType()))
                .onFailure().invoke(e -> log.error("Failed to assign user {} to group '{}' ({}): {}",
                        context.getCreatedEntity().getId(), groupRef.getValue(), groupRef.getType(), e.getMessage(), e))
                .onFailure().recoverWithItem((Void) null);
    }

    /**
     * Resolves a group reference to both internal group ID and auth server group ID.
     */
    private Uni<Tuple2<String, String>> resolveGroupIdAndAuthId(String tenantId, GroupReference groupRef) {
        return resolveGroupEntityFromReference(tenantId, groupRef)
                .map(groupEntity -> {
                    String groupId = groupEntity.getId();
                    String authId = Optional.ofNullable(groupEntity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString)
                            .filter(StringUtils::isNoneBlank)
                            .orElseThrow(() -> new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                                    "Group authId not found for group: " + groupId));
                    return Tuple2.of(groupId, authId);
                });
    }

    /**
     * Resolves a group reference to the actual group entity based on reference type.
     */
    private Uni<EntityWithPermission> resolveGroupEntityFromReference(String tenantId, GroupReference groupRef) {
        return switch (groupRef.getType()) {
            case ID -> userGroupService.getById(tenantId, groupRef.getValue());
            case NAME -> {
                if (groupRef.shouldCreateIfNotExists()) {
                    yield userGroupService.findOrCreateGroupByName(tenantId, groupRef.getValue().trim());
                } else {
                    yield userGroupService.findGroupByName(tenantId, groupRef.getValue().trim());
                }
            }
            case EXTERNAL_ID -> userGroupService.findGroupByExternalId(tenantId, groupRef.getValue());
            case CODE -> userGroupService.findGroupByCode(tenantId, groupRef.getValue());
        };
    }

    private void validateContextForPermissionAssignment(UserCreationContext context) {
        if (!context.isReadyForPermissionAssignment()) {
            throw new IllegalStateException("Context not ready for permission assignment");
        }
    }

    private Map<String, String> getValidGlideRoleMap(String tenantId, SecurityProviderService securityProviderService) {
        // Implementation would be moved from UserService
        // For now, returning empty map to avoid compilation errors
        return Map.of();
    }

    private Set<String> getValidRolesToAdd(String tenantId, Map<String, String> validGlideRoles, Set<String> requestRoles) {
        // Implementation would be moved from UserService
        // For now, returning the request roles as-is
        return requestRoles;
    }
}
