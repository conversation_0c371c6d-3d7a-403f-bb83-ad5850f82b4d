/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.repository.RoleRepository;
import com.tripudiotech.authservice.request.AccountUpdateRequest;
import com.tripudiotech.authservice.request.EntityUnderCompanyRequest;
import com.tripudiotech.authservice.response.AccountCreatedResponse;
import com.tripudiotech.authservice.response.RoleResponse;
import com.tripudiotech.authservice.service.context.UserCreationContext;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.GroupReference;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.client.dto.request.notification.Subscriber;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.PermissionDeniedException;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.base.service.NotificationService;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.OrgRole;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import com.tripudiotech.securitylib.dto.request.AuthServerPersonRequest;
import com.tripudiotech.securitylib.dto.request.PersonRequest;
import com.tripudiotech.securitylib.dto.response.RoleDto;
import com.tripudiotech.securitylib.exception.RestClientException;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.tripudiotech.authservice.constant.EntityTypeConstants.PERSON_ENTITY_TYPE;
import static com.tripudiotech.authservice.constant.SearchingParameterConstants.EMAIL;
import static com.tripudiotech.authservice.constant.SearchingParameterConstants.PAGE_SIZE;
import static com.tripudiotech.base.constant.RequestConstants.LIMIT_REQUEST_PARAM;

@ApplicationScoped
@Slf4j
public class UserService {

    @Inject
    @RestClient
    EntityServiceClient entityRepository;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ObjectMapper objectMapper;

    @Inject
    RoleRepository roleRepository;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    NotificationService notificationService;

    @Inject
    UserGroupService userGroupService;

    @Inject
    CacheService cacheService;

    @Inject
    AsyncConfigurationService asyncConfigurationService;

    @Inject
    UserValidationService userValidationService;

    @Inject
    UserPermissionService userPermissionService;

    @Inject
    UserProtectionService userProtectionService;

    @ConfigProperty(name = "application.triggerVerifyEmail")
    boolean triggerVerifyEmail;

    @ConfigProperty(name = "application.triggerNotifyEmail")
    boolean triggerNotifyEmail;

    @ConfigProperty(name = "quarkus.http.timeout")
    Duration httpTimeout;

    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @SuppressWarnings("rawtypes")
    public Uni<PageResponse<List<UserRealmInformationResponse>>> searchUser(String tenantId, MultivaluedMap<String, String> queryParams) {
        return Uni.createFrom().item(() -> {
            MultivaluedMap<String, String> parameters = new MultivaluedHashMap<>(queryParams);
            parameters.putSingle(PAGE_SIZE, Optional.ofNullable(queryParams.getFirst(LIMIT_REQUEST_PARAM)).orElse(RequestConstants.DEFAULT_LIMIT));
            parameters.putSingle(SearchingParameterConstants.PAGE_NUMBER, Optional.ofNullable(queryParams.getFirst(RequestConstants.OFFSET_REQUEST_PARAM)).orElse(RequestConstants.DEFAULT_OFFSET));

            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
            List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, parameters);
            long totalUser = securityProviderService.countUsers(tenantId, parameters);
            long limit = Long.parseLong(parameters.getFirst(PAGE_SIZE));

            PageResponse<List<UserRealmInformationResponse>> pageResponse = PageResponse.<List<UserRealmInformationResponse>>builder()
                    .data(Collections.singletonList(userResponseList))
                    .pageInfo(new PageInfo(totalUser, limit, userResponseList.size()))
                    .build();

            return pageResponse;
        }).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    public Uni<UserInformation> getUserDetail(@NonNull String tenantId) {
        return Uni.createFrom().item(() ->
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(tenantId)
        ).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Void> removeRoleFromUser(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String userId,
            String roleToDelete
    ) {
        if (StringUtils.isBlank(roleToDelete)) {
            throw new BadRequestException(tenantId, "Request role can not be empty or blank");
        }

        return getUserInAuthServerFrom(tenantId, userId)
                .flatMap(userInAuthServer -> Uni.createFrom().item(() -> {
                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                            Map<String, String> validGlideRoleNamesMap = getValidGlideRoleMap(tenantId, securityProviderService);
                            String actualRoleNameToDelete = validGlideRoleNamesMap.get(roleToDelete.toLowerCase());

                            if (StringUtils.isBlank(actualRoleNameToDelete)) {
                                throw new BadRequestException(tenantId, "Role name [" + roleToDelete + "] is invalid");
                            }

                            return Tuple2.of(securityProviderService, actualRoleNameToDelete);
                        })
                        .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                        .flatMap(tuple -> {
                            SecurityProviderService securityProviderService = tuple.getItem1();
                            String actualRoleNameToDelete = tuple.getItem2();

                            return ReactorUtils.toUni(roleRepository.getAllRoles(tenantId, userInformation))
                                    .flatMap(orgRoles -> {
                                        OrgRole orgRole = orgRoles.stream()
                                                .filter(role -> role.getName().equalsIgnoreCase(roleToDelete))
                                                .findFirst()
                                                .orElseThrow(() -> new BadRequestException(tenantId, "Role name [" + roleToDelete + "] is invalid"));

                                        return getUserEntityByEmail(tenantId, userInAuthServer.getEmail())
                                                .flatMap(personEntity -> Uni.createFrom().item(() -> {
                                                            // Remove role from auth server first
                                                            securityProviderService.removeRole(tenantId, userInAuthServer.getId(), actualRoleNameToDelete);
                                                            return personEntity;
                                                        })
                                                        .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                                                        .flatMap(entity -> {
                                                            // Remove role from database
                                                            return roleRepository.removeRolesFromPerson(tenantId, userInformation,
                                                                            List.of(orgRole), SysRoot.builder().id(entity.getId()).build())
                                                                    .onFailure().invoke(e -> {
                                                                        log.error("Failed to remove {} from Person {} to OrgRole {}",
                                                                                DBConstants.RELATION_HAS_ROLE, userId, orgRole.getId());
                                                                        // Rollback
                                                                        log.info("Rolling back removing role {} from user {}", roleToDelete, userId);
                                                                        try {
                                                                            securityProviderService.assignDefaultRolesToUser(tenantId,
                                                                                    userInAuthServer.getId(), Set.of(actualRoleNameToDelete));
                                                                        } catch (Exception rollbackException) {
                                                                            log.error("Failed to rollback role assignment", rollbackException);
                                                                        }
                                                                    });
                                                        }));
                                    });
                        }));
    }

    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Void> assignRoleToUser(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String userId,
            Set<String> requestRoles
    ) {
        if (requestRoles == null || requestRoles.isEmpty()) {
            throw new BadRequestException(tenantId, "Request roles cannot be empty");
        }

        return Uni.createFrom().item(() -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    Map<String, String> validGlideRoleNamesMap = getValidGlideRoleMap(tenantId, securityProviderService);
                    Set<String> validRolesToAdd = getValidRolesToAdd(tenantId, validGlideRoleNamesMap, requestRoles);
                    return Tuple2.of(validGlideRoleNamesMap, validRolesToAdd);
                })
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(tuple -> {
                    Set<String> validRolesToAdd = tuple.getItem2();
                    return getUserInAuthServerFrom(tenantId, userId)
                            .flatMap(userInAuthServer -> {
                                SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                securityProviderService.assignDefaultRolesToUser(tenantId, userInAuthServer.getId(), validRolesToAdd);

                                Set<String> validRolesToAddLowerCase = validRolesToAdd.stream()
                                        .map(String::toLowerCase)
                                        .collect(Collectors.toSet());

                                return ReactorUtils.toUni(roleRepository.getAllRoles(tenantId, userInformation))
                                        .flatMap(orgRoles -> {
                                            List<OrgRole> validOrgRoles = orgRoles.stream()
                                                    .filter(orgRole -> validRolesToAddLowerCase.contains(orgRole.getName().toLowerCase()))
                                                    .toList();

                                            if (validOrgRoles.isEmpty()) {
                                                throw new BadRequestException(tenantId, "No valid roles to assign");
                                            }
                                            log.info("Assigning roles {} to user {}", validOrgRoles, userId);

                                            return roleRepository.assignRoleToPerson(tenantId, userInformation, validOrgRoles,
                                                    SysRoot.builder().id(userId).build());
                                        });
                            });
                });
    }

    /**
     * Assigns roles to a user using the auth server user ID directly for optimal performance.
     * This method bypasses user lookup operations by accepting the auth server user ID directly,
     * eliminating redundant database and Keycloak API calls.
     *
     * @param tenantId        the tenant identifier
     * @param userInformation the current user information for authorization
     * @param userId          the internal user ID (for database operations)
     * @param authServerId    the auth server user ID (for Keycloak operations)
     * @param requestRoles    the set of roles to assign to the user
     * @return a Uni that completes when roles are successfully assigned
     */
    public Uni<Void> assignRolesToUserByAuthUserId(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String userId,
            @NonNull String authServerId,
            Set<String> requestRoles
    ) {
        if (requestRoles == null || requestRoles.isEmpty()) {
            throw new BadRequestException(tenantId, "Request roles cannot be empty");
        }

        return Uni.createFrom().item(() -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    Map<String, String> validGlideRoleNamesMap = getValidGlideRoleMap(tenantId, securityProviderService);
                    return getValidRolesToAdd(tenantId, validGlideRoleNamesMap, requestRoles);
                })
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(validRolesToAdd -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    securityProviderService.assignDefaultRolesToUser(tenantId, authServerId, validRolesToAdd);

                    Set<String> validRolesToAddLowerCase = validRolesToAdd.stream()
                            .map(String::toLowerCase)
                            .collect(Collectors.toSet());

                    return ReactorUtils.toUni(roleRepository.getAllRoles(tenantId, userInformation))
                            .flatMap(orgRoles -> {
                                List<OrgRole> validOrgRoles = orgRoles.stream()
                                        .filter(orgRole -> validRolesToAddLowerCase.contains(orgRole.getName().toLowerCase()))
                                        .toList();

                                if (validOrgRoles.isEmpty()) {
                                    throw new BadRequestException(tenantId, "No valid roles to assign");
                                }
                                log.info("Assigning roles {} to user {}", validOrgRoles, userId);

                                return roleRepository.assignRoleToPerson(tenantId, userInformation, validOrgRoles,
                                        SysRoot.builder().id(userId).build());
                            });
                });
    }

    public Uni<UserRealmInformationResponse> getUserInAuthServerFrom(
            @NonNull String tenantId,
            @NonNull String userIdInDB
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getEntityDetail(token, tenantId, PERSON_ENTITY_TYPE, userIdInDB)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(personEntity -> {
                    String email = Optional.ofNullable(personEntity.getProperties().get(DBConstants.USER_EMAIL_KEY_PROPERTY))
                            .map(Object::toString)
                            .map(StringUtils::trim)
                            .filter(StringUtils::isNoneBlank)
                            .orElseThrow(() -> new RecordNotFoundException(tenantId, "Unable to find user email for user id " + userIdInDB));
                    return getUsersByEmail(tenantId, email)
                            .map(userResponseList -> userResponseList.stream()
                                    .findFirst()
                                    .orElseThrow(() -> new RecordNotFoundException(tenantId, "Unable to find user in auth server for user id " + userIdInDB)));
                })
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    /**
     * Creates a new user account with role and group assignments.
     * This method orchestrates the complete user creation workflow.
     *
     * @param tenantId        the tenant identifier
     * @param userInformation current user information for authorization
     * @param companyId       the company identifier where user will be created
     * @param request         user creation request containing user details and group assignments
     * @return created user entity with permissions
     */
    public Uni<EntityWithPermission> create(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String companyId,
            @NonNull AccountRequest request
    ) {
        UserCreationContext context = UserCreationContext.builder()
                .tenantId(tenantId)
                .userInformation(userInformation)
                .companyId(companyId)
                .request(request)
                .resolvedEmail(normalizeEmail(request.getUsername()))
                .token(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()))
                .build();

        log.info("Starting user creation for email: {} in company: {} (tenant: {})",
                context.getResolvedEmail(), companyId, tenantId);

        return validateUserCreationRequest(context)
                .chain(validatedContext -> executeUserCreationWorkflow(validatedContext))
                .onItem().invoke(result -> logSuccessfulCreation(context, result))
                .onFailure().recoverWithUni(throwable -> handleUserCreationFailure(context, throwable));
    }

    /**
     * Normalizes email address to lowercase and trims whitespace.
     */
    private String normalizeEmail(String email) {
        return userValidationService.validateAndNormalizeEmail(email);
    }

    /**
     * Validates the user creation request and prepares the context.
     */
    private Uni<UserCreationContext> validateUserCreationRequest(UserCreationContext context) {
        return userValidationService.validateAccountRequest(context.getTenantId(), context.getRequest())
                .map(ignored -> {
                    // Prepare request for entity creation
                    context.getRequest().getProperties().put(DBConstants.USER_EMAIL_KEY_PROPERTY, context.getResolvedEmail());
                    context.getRequest().setCompanyId(context.getCompanyId());
                    return context;
                });
    }

    /**
     * Executes the complete user creation workflow.
     */
    private Uni<EntityWithPermission> executeUserCreationWorkflow(UserCreationContext context) {
        return validateCompanyExists(context)
                .chain(validatedContext -> createUserEntity(validatedContext))
                .chain(contextWithEntity -> createAuthServerUser(contextWithEntity))
                .chain(contextWithAuth -> assignUserPermissions(contextWithAuth))
                .chain(finalContext -> handlePostCreationActivities(finalContext))
                .map(UserCreationContext::getCreatedEntity);
    }

    /**
     * Validates that the company exists and user has permission to create users in it.
     */
    private Uni<UserCreationContext> validateCompanyExists(UserCreationContext context) {
        return validateCompany(context.getTenantId(), context.getCompanyId(), context.getToken())
                .map(companyEntity -> context.withCompanyEntity(companyEntity));
    }

    /**
     * Creates the user entity in the database.
     */
    private Uni<UserCreationContext> createUserEntity(UserCreationContext context) {
        EntityUnderCompanyRequest entityRequest = new EntityUnderCompanyRequest(
                context.getCompanyId(),
                context.getRequest().getProperties()
        );

        return createPersonEntity(context.getTenantId(), context.getToken(), entityRequest)
                .map(personEntity -> context.withCreatedEntity(personEntity));
    }

    /**
     * Creates the user in the authentication server (Keycloak).
     */
    private Uni<UserCreationContext> createAuthServerUser(UserCreationContext context) {
        return createKeycloakUser(
                context.getTenantId(),
                context.getCompanyId(),
                context.getRequest(),
                context.getCreatedEntity()
        ).map(tuple -> context.withAuthServerId(tuple.getItem2()));
    }

    /**
     * Assigns default roles and requested groups to the user.
     */
    private Uni<UserCreationContext> assignUserPermissions(UserCreationContext context) {
        Uni<Void> roleAssignment = userPermissionService.assignDefaultRoles(context);
        Uni<Void> groupAssignment = userPermissionService.assignUserToGroups(context);

        return Uni.combine().all().unis(roleAssignment, groupAssignment)
                .discardItems()
                .map(ignored -> context);
    }

    /**
     * Handles post-creation activities like email verification and notifications.
     */
    private Uni<UserCreationContext> handlePostCreationActivities(UserCreationContext context) {
        return handlePostCreationTasks(
                context.getTenantId(),
                context.getCreatedEntity(),
                context.getAuthServerId(),
                context.getRequest()
        ).map(result -> context);
    }

    private void logSuccessfulCreation(UserCreationContext context, EntityWithPermission result) {
        log.info("User creation completed successfully for email: {} (ID: {})",
                context.getResolvedEmail(), result.getId());
    }

    private Uni<EntityWithPermission> handleUserCreationFailure(UserCreationContext context, Throwable throwable) {
        log.error("Failed to create user for email: {} in company: {}",
                context.getResolvedEmail(), context.getCompanyId(), throwable);
        return handleCreationFailure(context.getTenantId(), throwable, context.getRequest());
    }


    private Uni<EntityWithPermission> validateCompany(String tenantId, String companyId, String token) {
        // Try cache first
        return cacheService.getCachedCompanyValidation(tenantId, companyId)
                .onItem().ifNotNull().transform(cachedCompany -> {
                    log.debug("Using cached company validation for: {}", companyId);
                    return cachedCompany;
                })
                .onItem().ifNull().switchTo(() -> {
                    // Cache miss - fetch from database
                    log.debug("Cache miss for company validation, fetching from database: {}", companyId);
                    return getEntityById(
                            entityRepository.getEntityDetail(token, tenantId, SysRoot.class.getSimpleName(), companyId),
                            tenantId
                    ).onItem().transform(companyEntity -> {
                        if (companyEntity.getId() == null) {
                            throw new RecordNotFoundException(tenantId, "Company not found");
                        }
                        // Cache the result for future use
                        cacheService.cacheCompanyValidation(tenantId, companyId, companyEntity)
                                .subscribe().with(
                                        success -> log.debug("Cached company validation for: {}", companyId),
                                        failure -> log.warn("Failed to cache company validation: {}", failure.getMessage())
                                );
                        return companyEntity;
                    }).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
                });
    }

    private Uni<EntityWithPermission> createPersonEntity(
            String tenantId,
            String token,
            EntityUnderCompanyRequest request
    ) {
        return entityRepository.createEntity(
                        token,
                        tenantId,
                        PERSON_ENTITY_TYPE,
                        CreateEntityRequest.builder()
                                .relations(request.getRelations())
                                .attributes(request.getAttributes())
                                .build()
                ).map(response -> response.readEntity(EntityWithPermission.class))
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    private Uni<Tuple2<EntityWithPermission, String>> createKeycloakUser(
            String tenantId,
            String companyId,
            AccountRequest request,
            EntityWithPermission personEntity
    ) {
        SecurityProviderService securityProviderService =
                securityProviderServiceFactory.getDefaultAuthenticateService();

        return Uni.createFrom().item(() -> {
            try (Response response = securityProviderService.createUser(
                    tenantId,
                    AuthServerPersonRequest.builder()
                            .referenceId(personEntity.getId())
                            .companyId(companyId)
                            .email(request.getUsername())
                            .username(request.getUsername())
                            .firstName(request.getFirstName())
                            .lastName(request.getLastName())
                            .enabled(true)
                            .build()
            )) {
                response.bufferEntity();
                if (!Response.Status.Family.familyOf(response.getStatus())
                        .equals(Response.Status.Family.SUCCESSFUL)) {

                    String errorMessage = response.readEntity(String.class);

                    // Handle duplicate email error specifically
                    if (response.getStatus() == 409 ||
                        (errorMessage != null && errorMessage.toLowerCase().contains("email") &&
                         (errorMessage.toLowerCase().contains("exists") || errorMessage.toLowerCase().contains("duplicate")))) {
                        throw new BadRequestException(tenantId,
                                String.format("The email address [%s] already exists", request.getUsername()));
                    }

                    throw new ServiceException(
                            tenantId,
                            BusinessErrorCode.fromHttpStatusCode(response.getStatus()),
                            errorMessage
                    );
                }

                String authServerId = Optional.ofNullable(response.getHeaderString("location"))
                        .map(location -> location.split("/"))
                        .map(splits -> splits[splits.length - 1])
                        .orElseThrow(() -> new ServiceException(
                                tenantId,
                                BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                                "Failed to get auth server ID"
                        ));

                return Tuple2.of(personEntity, authServerId);
            }
        }).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }





    private Uni<EntityWithPermission> handlePostCreationTasks(
            String tenantId,
            EntityWithPermission personEntity,
            String authServerId,
            AccountRequest request
    ) {
        // Check if async post-creation tasks are enabled
        if (!asyncConfigurationService.isPostCreationTasksEnabled()) {
            log.debug("Async post-creation tasks are disabled for user {}", authServerId);
            return Uni.createFrom().item(new AccountCreatedResponse(authServerId, request.getCompanyId(), personEntity))
                    .onItem().transform(response -> (EntityWithPermission) response);
        }

        // Create async tasks for email verification and notifications
        Uni<Void> emailVerificationTask = createEmailVerificationTask(tenantId, authServerId);
        Uni<Void> notificationTask = createNotificationTask(personEntity, request, authServerId);

        // Combine both tasks and execute them asynchronously
        Uni<Void> combinedAsyncTasks = Uni.combine().all().unis(emailVerificationTask, notificationTask)
                .discardItems()
                .onItem().invoke(() -> log.info("All post-creation tasks completed for user {}", authServerId))
                .onFailure().invoke(throwable ->
                        log.warn("Some post-creation tasks failed for user {}: {}", authServerId, throwable.getMessage())
                );

        // Execute the combined tasks using the custom async service (fire and forget)
        asyncConfigurationService.executeAsyncVoid(combinedAsyncTasks)
                .subscribe().with(
                        success -> log.debug("Post-creation tasks submitted successfully for user {}", authServerId),
                        failure -> log.warn("Failed to submit post-creation tasks for user {}: {}", authServerId, failure.getMessage())
                );

        // Return immediately with the created user response
        return Uni.createFrom().item(new AccountCreatedResponse(authServerId, request.getCompanyId(), personEntity))
                .onItem().transform(response -> (EntityWithPermission) response);
    }

    /**
     * Create email verification task
     */
    private Uni<Void> createEmailVerificationTask(String tenantId, String authServerId) {
        if (!triggerVerifyEmail) {
            log.debug("Email verification is disabled, skipping for user {}", authServerId);
            return Uni.createFrom().voidItem();
        }

        return Uni.createFrom().item(() -> {
                    SecurityProviderService securityProviderService =
                            securityProviderServiceFactory.getDefaultAuthenticateService();
                    securityProviderService.sendVerifyEmailToUser(tenantId, authServerId);
                    log.info("Email verification sent for user {}", authServerId);
                    return (Void) null;
                })
                .onFailure().invoke(throwable ->
                        log.error("Failed to send email verification for user {}: {}", authServerId, throwable.getMessage())
                );
    }

    /**
     * Create notification task (Novu integration)
     */
    private Uni<Void> createNotificationTask(EntityWithPermission personEntity, AccountRequest request, String authServerId) {
        if (!triggerNotifyEmail) {
            log.debug("Email notifications are disabled, skipping for user {}", authServerId);
            return Uni.createFrom().voidItem();
        }

        return Uni.createFrom().item(() -> {
                    linkUserWithNovu(personEntity, request);
                    log.info("Novu notification sent for user {}", authServerId);
                    return (Void) null;
                })
                .onFailure().invoke(throwable ->
                        log.error("Failed to send Novu notification for user {}: {}", authServerId, throwable.getMessage())
                );
    }

    private Uni<EntityWithPermission> handleCreationFailure(
            String tenantId,
            Throwable throwable,
            AccountRequest request
    ) {
        return Uni.createFrom().failure(() -> {
            log.error("Failed to create user with email {}", request.getUsername(), throwable);

            if (throwable instanceof ServiceException) {
                return throwable;
            }
            return new ServiceException(
                    tenantId,
                    BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                    "Failed to create user: " + throwable.getMessage()
            );
        });
    }

    private Uni<EntityWithPermission> getEntityById(Uni<Response> entityRepository, @NotNull String tenantId) {
        return entityRepository
                .onItem().transform(response -> {
                    response.bufferEntity();
                    if (Response.Status.Family.familyOf(response.getStatus())
                            .equals(Response.Status.Family.SUCCESSFUL)) {
                        return response.readEntity(EntityWithPermission.class);
                    } else {
                        throw new ServiceException(
                                tenantId,
                                BusinessErrorCode.fromHttpStatusCode(response.getStatus()),
                                response.readEntity(String.class)
                        );
                    }
                })
                .onFailure().transform(throwable -> {
                    if (throwable instanceof ServiceException) {
                        return throwable;
                    }
                    return new ServiceException(
                            tenantId,
                            BusinessErrorCode.SERVICE_INTERNAL_ERROR,
                            "Failed to get entity: " + throwable.getMessage()
                    );
                });
    }

    private Set<String> getValidRolesToAdd(
            @NonNull String tenantId,
            @NonNull Map<String, String> validGlideRoles,
            @NonNull Set<String> requestRoles
    ) {
        Set<String> validRolesInLowerCase = validGlideRoles.keySet();
        Set<String> invalidRoles = requestRoles.stream()
                .map(String::toLowerCase)
                .map(StringUtils::trim)
                .collect(Collectors.toSet());
        invalidRoles.removeAll(validRolesInLowerCase);
        if (invalidRoles.isEmpty()) {
            Set<String> correctRolesInCaseSensitive = new HashSet<>();
            for (String requestRole : requestRoles) {
                correctRolesInCaseSensitive.add(validGlideRoles.get(requestRole.toLowerCase()));
            }
            return correctRolesInCaseSensitive;
        }
        throw new BadRequestException(tenantId,
                String.format("Roles %s are not supported!", StringUtils.join(invalidRoles)));
    }

    public Uni<EntityWithPermission> getUserDetail(@NonNull String tenantId, @NonNull String authServerUserId) {
        return Uni.createFrom().item(() ->
                        securityProviderServiceFactory.getDefaultAuthenticateService().getUserById(tenantId, authServerUserId)
                )
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(authServerUser ->
                        getUserEntityByEmail(tenantId, authServerUser.getEmail())
                                .map(entityWithPermission -> {
                                    // admin & superadmin, does not have company
                                    return new AccountCreatedResponse(authServerUser,
                                            Optional.ofNullable(authServerUser.getAttributes().get("companyId"))
                                                    .filter(list -> !list.isEmpty())
                                                    .map(list -> list.get(0)).orElse("undefined"),
                                            entityWithPermission);
                                })
                );
    }

    public Uni<Void> changeUserStatus(@NonNull String tenantId,
                                      @NonNull String authServerUserId,
                                      boolean enable) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return Uni.createFrom().item(() ->
                        securityProviderServiceFactory.getDefaultAuthenticateService().getUserById(tenantId, authServerUserId)
                )
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(keycloakUser -> {
                    // Use UserProtectionService for clean validation
                    userProtectionService.validateUserStatusChange(tenantId, keycloakUser, enable);

                    return getUserEntityByEmail(tenantId, keycloakUser.getEmail())
                            .flatMap(entityWithPermission -> {
                                PersonRequest accountRequest = PersonRequest.builder()
                                        .email(keycloakUser.getEmail())
                                        .username(keycloakUser.getUsername())
                                        .firstName(keycloakUser.getFirstName())
                                        .lastName(keycloakUser.getLastName())
                                        .enabled(enable)
                                        .build();

                                return Uni.createFrom().item(() -> {
                                            // Update user in auth server first
                                            securityProviderServiceFactory.getDefaultAuthenticateService().updateUser(tenantId, keycloakUser.getId(), accountRequest);
                                            return accountRequest;
                                        })
                                        .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                                        .flatMap(request -> {
                                            // Check if state change is needed
                                            boolean shouldNotChangeState = (enable && entityWithPermission.getState().get(DBConstants.NAME_PROPERTY).toString().equalsIgnoreCase("Active")) ||
                                                                           (!enable && entityWithPermission.getState().get(DBConstants.NAME_PROPERTY).toString().equalsIgnoreCase("InActive"));

                                            if (shouldNotChangeState) {
                                                return Uni.createFrom().voidItem();
                                            }

                                            // Update entity state
                                            Uni<Response> stateUpdateUni = enable ?
                                                    entityRepository.demote(token, tenantId, entityWithPermission.getId(), "Active") :
                                                    entityRepository.promote(token, tenantId, entityWithPermission.getId(), "InActive");

                                            return stateUpdateUni
                                                    .onItem().invoke(response -> {
                                                        if (response.getStatus() != HttpStatus.SC_OK) {
                                                            throw new RestClientException(response);
                                                        }
                                                    })
                                                    .onFailure().invoke(exception -> {
                                                        // Rollback auth server changes
                                                        log.error("Failed to update entity state, rolling back auth server changes", exception);
                                                        try {
                                                            securityProviderServiceFactory.getDefaultAuthenticateService().updateUser(tenantId, keycloakUser.getId(),
                                                                    request.toBuilder().enabled(!enable).build());
                                                        } catch (Exception rollbackException) {
                                                            log.error("Failed to rollback auth server changes", rollbackException);
                                                        }
                                                    })
                                                    .replaceWithVoid();
                                        });
                            });
                });
    }

    public Uni<EntityWithPermission> update(String tenantId, String authServerUserId, AccountUpdateRequest request) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return Uni.createFrom().item(() ->
                        securityProviderServiceFactory.getDefaultAuthenticateService().getUserById(tenantId, authServerUserId)
                )
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                .flatMap(existingKeycloakUser -> {
                    if (existingKeycloakUser.getEnabled() == null || !existingKeycloakUser.getEnabled()) {
                        throw new BadRequestException(tenantId, "You are attempting to update an inactive user");
                    }

                    // Use UserProtectionService for clean validation
                    userProtectionService.validateUserCanBeUpdated(tenantId, existingKeycloakUser);

                    return getUserEntityByEmail(tenantId, existingKeycloakUser.getEmail())
                            .flatMap(originalEntityWithPermission -> {
                                // Check if new email is duplicated in keycloak system or not ?
                                String originalEmail = originalEntityWithPermission.getProperties().get("email").toString();
                                String newEmailRequest = Optional.ofNullable(request.getProperties().get("email")).orElse(originalEmail).toString().toLowerCase(Locale.ROOT);
                                boolean isUserEmailChanged = !originalEmail.equalsIgnoreCase(newEmailRequest);

                                Uni<Boolean> emailValidation = isUserEmailChanged ?
                                        isEmailExistedInKeycloak(tenantId, newEmailRequest) :
                                        Uni.createFrom().item(false);

                                return emailValidation.flatMap(emailExists -> {
                                    if (emailExists) {
                                        throw new BadRequestException(tenantId, String.format("The email address [%s] already existed", newEmailRequest));
                                    }

                                    // Do Update in neo4j
                                    Map<String, Object> updatedProperties = new HashMap<>(request.getProperties());
                                    updatedProperties.put("description", Optional.ofNullable(request.getDescription()).filter(StringUtils::isNoneBlank)
                                            .orElseGet(() -> Optional.ofNullable(originalEntityWithPermission.getProperties().get("description")).map(Objects::toString).orElse(null)));
                                    updatedProperties.put("firstName", request.getFirstName());
                                    updatedProperties.put("lastName", request.getLastName());
                                    updatedProperties.put("email", isUserEmailChanged ? newEmailRequest : existingKeycloakUser.getEmail());

                                    return entityRepository.updateEntity(token, tenantId, originalEntityWithPermission.getId(),
                                                    UpdateEntityRequest.builder()
                                                            .attributes(updatedProperties)
                                                            .build())
                                            .map(response -> response.readEntity(EntityWithPermission.class))
                                            .flatMap(updatedEntity -> {
                                                // Do update in keycloak
                                                return Uni.createFrom().item(() -> {
                                                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                                            securityProviderService.updateUser(tenantId, existingKeycloakUser.getId(), PersonRequest.builder()
                                                                    .email(isUserEmailChanged ? newEmailRequest : existingKeycloakUser.getEmail())
                                                                    .username(isUserEmailChanged ? newEmailRequest : existingKeycloakUser.getEmail())
                                                                    .firstName(request.getFirstName())
                                                                    .lastName(request.getLastName())
                                                                    .build());
                                                            return updatedEntity;
                                                        })
                                                        .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                                                        .onFailure().recoverWithUni(exception -> {
                                                            log.error("Unable to send request to update user in keycloak, rolling back the changed in DB successfully", exception);
                                                            return entityRepository.updateEntity(token, tenantId, originalEntityWithPermission.getId(),
                                                                            UpdateEntityRequest.builder()
                                                                                    .attributes(originalEntityWithPermission.getProperties())
                                                                                    .build())
                                                                    .flatMap(rollbackResponse -> Uni.createFrom().failure(exception));
                                                        });
                                            });
                                });
                            });
                });
    }

    private Uni<Boolean> isEmailExistedInKeycloak(
            @NonNull String tenantId,
            @NonNull String newEmailRequested
    ) {
        return getUsersByEmail(tenantId, newEmailRequested)
                .map(users -> users.stream().findFirst().isPresent());
    }

    public Uni<List<UserRealmInformationResponse>> getUsersByEmail(
            @NonNull String tenantId,
            @NonNull String emailRequested
    ) {
        return Uni.createFrom().item(() -> {
            MultivaluedMap<String, String> parameters = new MultivaluedHashMap<>();
            parameters.putSingle(SearchingParameterConstants.PAGE_SIZE, "1");
            parameters.putSingle(EMAIL, emailRequested);

            log.info("Search users with email {}", emailRequested);

            return securityProviderServiceFactory.getDefaultAuthenticateService().searchUsers(tenantId, parameters);
        }).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }


    public Uni<EntityWithPermission> getUserEntityByEmail(@NonNull String tenantId, @NonNull String email) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        String query = new JSONObject().put("$exact", new JSONObject().put("email", email.toLowerCase())).toString();
        return entityRepository.getAll(token, tenantId, PERSON_ENTITY_TYPE, 0, 1, query, null, null, null, null)
                .map(response -> response.readEntity(PageResponse.class))
                .map(pageResponse -> {
                    @SuppressWarnings("unchecked")
                    EntityWithPermission entityWithPermission = (EntityWithPermission) pageResponse.getData().stream()
                            .findFirst()
                            .map(record -> objectMapper.convertValue(record, EntityWithPermission.class))
                            .orElse(null);

                    if (Objects.isNull(entityWithPermission)) {
                        throw new RecordNotFoundException(tenantId, String.format("Unable to find account with email [%s]", email));
                    }
                    return entityWithPermission;
                });
    }

    public Uni<List<RoleDto>> getRoles(String tenantId) {
        // Try cache first
        return cacheService.getCachedRoles(tenantId)
                .onItem().ifNotNull().transform(cachedRoles -> {
                    log.debug("Using cached roles for tenant: {}", tenantId);
                    return cachedRoles;
                })
                .onItem().ifNull().switchTo(() -> {
                    // Cache miss - fetch from security provider
                    log.debug("Cache miss for roles, fetching from security provider for tenant: {}", tenantId);
                    return Uni.createFrom().item(() ->
                                    securityProviderServiceFactory.getDefaultAuthenticateService().getRoles(tenantId)
                            )
                            .runSubscriptionOn(asyncConfigurationService.getCustomExecutor())
                            .onItem().invoke(roles -> {
                                // Cache the result for future use
                                cacheService.cacheRoles(tenantId, roles)
                                        .subscribe().with(
                                                success -> log.debug("Cached roles for tenant: {}", tenantId),
                                                failure -> log.warn("Failed to cache roles: {}", failure.getMessage())
                                        );
                            });
                });
    }

    public Uni<List<RoleResponse>> getAssignedUserRoles(String tenantId, String userId) {
        return Uni.createFrom().item(() -> {
            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
            Map<String, String> validGlideRoleNamesMap = getValidGlideRoleMap(tenantId, securityProviderService);
            Set<String> glideYokeRole = validGlideRoleNamesMap.keySet();
            // get assigned role
            List<RoleDto> mappingRole = securityProviderService.getRoleMapping(tenantId, userId);
            List<RoleResponse> result = new ArrayList<>();
            // get only glideyoke role
            mappingRole.forEach(r -> {
                if (glideYokeRole.stream().anyMatch(name -> name.equalsIgnoreCase(r.getName()))) {
                    result.add(RoleResponse.builder().id(r.getId()).name(r.getName()).build());
                }
            });
            return result;
        }).runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    private Map<String, String> getValidGlideRoleMap(String tenantId, SecurityProviderService securityProviderService) {
        return securityProviderService
                .getRoles(tenantId)
                .stream()
                .filter(RoleDto::isGlideRole)
                .map(RoleDto::getName)
                .filter(StringUtils::isNoneBlank)
                .collect(Collectors.toMap(String::toLowerCase, roleName -> roleName));
    }

    /*
        To receive the notification via Novu we should use subscriberId
     */
    private void linkUserWithNovu(EntityWithPermission createPersonResponse,
                                  AccountRequest request) {
        notificationService.createSubscriber(Subscriber.
                        builder()
                        .subscriberId(createPersonResponse.getId())
                        .email(String.valueOf(
                                createPersonResponse.getProperties().get(DBConstants.USER_EMAIL_KEY_PROPERTY)))
                        .firstName(request.getFirstName())
                        .lastName(request.getLastName())
                        .build()
                ).subscribe()
                .with(ok -> log.info("Successful link user with NOVU {} {}", ok.getStatus(),
                                ok.hasEntity() ? ok.getEntity() : ok.getStatus()),
                        throwable -> log.error("Unable to verify email", throwable)
                );
    }
}
