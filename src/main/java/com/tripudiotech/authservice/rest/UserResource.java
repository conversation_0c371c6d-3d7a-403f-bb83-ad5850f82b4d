/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import static com.tripudiotech.securitylib.constant.SecurityConstant.AUTHORIZATION_HEADER;
import static com.tripudiotech.securitylib.constant.SecurityConstant.TENANT_ID_HEADER;

import com.tripudiotech.authservice.service.UserService;
import com.tripudiotech.authservice.utils.GenericHelper;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.LoginRequestDTO;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.exception.InvalidLoginCredentialException;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.quarkus.security.Authenticated;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.HeaderParam;
import java.util.Optional;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Set;

@Path("/user")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
public class UserResource extends RestResource{

    @Inject
    UserService userService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @GET
    @Authenticated
    @Path("/me")
    public Uni<UserInformation> me() {
        return userService.getUserDetail(this.tenantId);
    }

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/roles")
    public Uni<Response> getRoles() {
        return userService.getRoles(this.tenantId)
                .map(roles -> Response.ok().entity(roles).build());
    }

    @GET
    @Operation(summary = "Get detail of a specific user with db user ID")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{userId}")
    public Uni<Response> getUserDetail(@PathParam("userId") String userId) {
        return userService.getUserDetail(this.tenantId, userId)
                .map(userDetail -> Response.ok(userDetail).build());
    }

    @POST
    @Operation(summary = "Assign roles to specific user")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{userId}")
    public Uni<Response> assignRoleToUser(@PathParam("userId") String userId, Set<String> roleNames) {
        return userService.getUserDetail(this.tenantId)
                .flatMap(userInformation ->
                    userService.assignRoleToUser(this.tenantId, userInformation, userId, roleNames)
                            .map(r -> Response.ok().build())
                );
    }

    @DELETE
    @Operation(summary = "Remove roles from specific user")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{userId}/role/{roleName}")
    public Uni<Response> removeUserRole(
            @PathParam("userId") String userId,
            @PathParam("roleName") String roleName) {
        return userService.getUserDetail(this.tenantId)
                .flatMap(userInformation ->
                    userService.removeRoleFromUser(this.tenantId, userInformation, userId, roleName)
                            .map(v -> Response.noContent().build())
                );
    }

    @GET
    @Operation(summary = "Get role assigned to user")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{userId}/role")
    public Uni<Response> getAssignedRole(@PathParam("userId") String userId) {
        return userService.getAssignedUserRoles(tenantId, userId)
                .map(roles -> Response.ok(roles).build());
    }

    @POST
    @Path("/impersonate/email/{email}")
    @SecurityRequirement(name = "basicAuth")
    public Uni<Response> impersonateUserEmail(@HeaderParam(TENANT_ID_HEADER) String tenantId, @PathParam("email") String email, @HeaderParam(AUTHORIZATION_HEADER) String basicAuth) {
        Optional<LoginRequestDTO> loginRequestDTO = Optional.ofNullable(GenericHelper.decodeAuthorizationBase64(basicAuth));
        if (loginRequestDTO.isEmpty()) {
            throw new InvalidLoginCredentialException();
        }
        return securityProviderServiceFactory.getDefaultAuthenticateService().impersonateWithUserEmail(tenantId, email, loginRequestDTO.get().getUsername(), loginRequestDTO.get().getPassword());
    }

    @POST
    @Path("/impersonate/id/{id}")
    @SecurityRequirement(name = "basicAuth")
    public Uni<Response> impersonateKeycloakUserId(@HeaderParam(TENANT_ID_HEADER) String tenantId, @PathParam("id") String id, @HeaderParam(AUTHORIZATION_HEADER) String basicAuth) {
        Optional<LoginRequestDTO> loginRequestDTO = Optional.ofNullable(GenericHelper.decodeAuthorizationBase64(basicAuth));
        if (loginRequestDTO.isEmpty()) {
            throw new InvalidLoginCredentialException();
        }
        return securityProviderServiceFactory.getDefaultAuthenticateService().impersonateWithUserId(tenantId, id, loginRequestDTO.get().getUsername(), loginRequestDTO.get().getPassword());
    }
}
