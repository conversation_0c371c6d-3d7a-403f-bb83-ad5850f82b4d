/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.model;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Set;

/**
 * Value object representing user protection information.
 * This immutable object encapsulates all information about why a user is protected
 * and what operations are restricted.
 * 
 * Following the Value Object pattern to create a rich domain model that's
 * easier to understand and maintain than primitive obsession.
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserProtectionInfo {
    
    /**
     * Whether the user is protected from modifications.
     */
    boolean isProtected;
    
    /**
     * The primary reason for protection (username-based, role-based, etc.).
     */
    ProtectionReason primaryReason;
    
    /**
     * Human-readable description of why the user is protected.
     */
    String description;
    
    /**
     * Set of operations that are restricted for this user.
     */
    Set<RestrictedOperation> restrictedOperations;
    
    /**
     * Additional context information about the protection.
     */
    String additionalContext;
    
    /**
     * Enumeration of possible protection reasons.
     */
    public enum ProtectionReason {
        USERNAME_BASED("Protected by username"),
        ROLE_BASED("Protected by role assignment"),
        SYSTEM_ACCOUNT("System account protection"),
        CUSTOM_RULE("Custom protection rule"),
        NOT_PROTECTED("User is not protected");
        
        private final String description;
        
        ProtectionReason(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Enumeration of operations that can be restricted.
     */
    public enum RestrictedOperation {
        STATUS_CHANGE("Status change (enable/disable)"),
        UPDATE("Profile updates"),
        DELETE("Account deletion"),
        ROLE_MODIFICATION("Role modifications"),
        PASSWORD_RESET("Password reset");
        
        private final String description;
        
        RestrictedOperation(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Creates a UserProtectionInfo for a non-protected user.
     */
    public static UserProtectionInfo notProtected() {
        return UserProtectionInfo.builder()
                .isProtected(false)
                .primaryReason(ProtectionReason.NOT_PROTECTED)
                .description("User is not protected")
                .restrictedOperations(Set.of())
                .build();
    }
    
    /**
     * Creates a UserProtectionInfo for a username-protected user.
     */
    public static UserProtectionInfo protectedByUsername(String username, Set<RestrictedOperation> operations) {
        return UserProtectionInfo.builder()
                .isProtected(true)
                .primaryReason(ProtectionReason.USERNAME_BASED)
                .description(String.format("User '%s' is a protected system administrator", username))
                .restrictedOperations(operations)
                .additionalContext("Protected usernames are configured in application properties")
                .build();
    }
    
    /**
     * Creates a UserProtectionInfo for a role-protected user.
     */
    public static UserProtectionInfo protectedByRole(Set<String> protectedRoles, Set<RestrictedOperation> operations) {
        return UserProtectionInfo.builder()
                .isProtected(true)
                .primaryReason(ProtectionReason.ROLE_BASED)
                .description(String.format("User has protected system roles: %s", String.join(", ", protectedRoles)))
                .restrictedOperations(operations)
                .additionalContext("Protected roles are configured in application properties")
                .build();
    }
    
    /**
     * Checks if a specific operation is restricted for this user.
     */
    public boolean isOperationRestricted(RestrictedOperation operation) {
        return isProtected && restrictedOperations.contains(operation);
    }
    
    /**
     * Gets a user-friendly error message for a restricted operation.
     */
    public String getErrorMessageForOperation(RestrictedOperation operation) {
        if (!isOperationRestricted(operation)) {
            return null;
        }
        
        return String.format("You are not allowed to perform %s on %s", 
                operation.getDescription().toLowerCase(), 
                description.toLowerCase());
    }
}
